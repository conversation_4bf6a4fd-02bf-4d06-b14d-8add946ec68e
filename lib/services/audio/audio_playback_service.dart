import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_pcm_sound/flutter_pcm_sound.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Audio playback state
enum AudioPlaybackState {
  idle,
  loading,
  playing,
  paused,
  stopped,
  error,
}

/// Service for playing TTS audio chunks using flutter_pcm_sound
class AudioPlaybackService {
  // State management
  AudioPlaybackState _state = AudioPlaybackState.idle;
  final _stateController = StreamController<AudioPlaybackState>.broadcast();
  final _errorController = StreamController<String>.broadcast();

  // Audio chunk management
  final List<Uint8List> _audioChunks = [];
  String? _currentRequestId;
  bool _isInitialized = false;
  bool _isPlaying = false;
  int _currentChunkIndex = 0;

  // Audio format tracking
  int _currentSampleRate = 16000; // Default to 16kHz (matches backend configuration)
  int _currentChannels = 1; // Default to mono

  // Getters
  Stream<AudioPlaybackState> get stateStream => _stateController.stream;
  Stream<String> get errorStream => _errorController.stream;
  AudioPlaybackState get currentState => _state;
  bool get isPlaying => _state == AudioPlaybackState.playing;
  bool get isLoading => _state == AudioPlaybackState.loading;

  /// Initialize the audio service with optional sample rate
  Future<void> initialize({int? sampleRate, int? channelCount}) async {
    final targetSampleRate = sampleRate ?? _currentSampleRate;
    final targetChannels = channelCount ?? _currentChannels;

    // Reinitialize if sample rate or channels changed
    if (_isInitialized && (targetSampleRate != _currentSampleRate || targetChannels != _currentChannels)) {
      debugPrint('AudioPlaybackService: Reinitializing due to format change: ${_currentSampleRate}Hz -> ${targetSampleRate}Hz, ${_currentChannels}ch -> ${targetChannels}ch');
      _isInitialized = false;
    }

    if (_isInitialized) return;

    try {
      debugPrint('AudioPlaybackService: Initializing PCM sound with ${targetSampleRate}Hz, ${targetChannels} channels...');

      // Update current format
      _currentSampleRate = targetSampleRate;
      _currentChannels = targetChannels;

      // Initialize flutter_pcm_sound
      await FlutterPcmSound.setup(
        sampleRate: _currentSampleRate,
        channelCount: _currentChannels,
      );

      // Set feed threshold for real-time audio (lower = more responsive)
      // Adjust threshold based on sample rate for consistent latency
      final feedThreshold = (_currentSampleRate * 0.25).round(); // ~250ms buffer
      await FlutterPcmSound.setFeedThreshold(feedThreshold);

      // Set up the callback for feeding audio data
      FlutterPcmSound.setFeedCallback(_onFeedCallback);

      _isInitialized = true;
      _setState(AudioPlaybackState.idle);

      debugPrint('AudioPlaybackService: PCM sound initialized successfully at ${_currentSampleRate}Hz, ${_currentChannels} channels, threshold: $feedThreshold');
    } catch (e) {
      _handleError('Failed to initialize PCM sound: $e');
    }
  }

  /// Callback method for flutter_pcm_sound to request more audio data
  void _onFeedCallback(int remainingFrames) async {
    if (!_isPlaying || _audioChunks.isEmpty) {
      return;
    }

    try {
      // Feed the next chunk if available
      if (_currentChunkIndex < _audioChunks.length) {
        final chunk = _audioChunks[_currentChunkIndex];

        // Convert bytes to List<int> for PcmArrayInt16
        final pcmData = <int>[];
        for (int i = 0; i < chunk.length; i += 2) {
          if (i + 1 < chunk.length) {
            // Convert little-endian 16-bit PCM to int
            final sample = (chunk[i + 1] << 8) | chunk[i];
            pcmData.add(sample);
          }
        }

        // Feed PCM data to flutter_pcm_sound
        await FlutterPcmSound.feed(PcmArrayInt16.fromList(pcmData));

        debugPrint('AudioPlaybackService: Fed chunk ${_currentChunkIndex + 1}/${_audioChunks.length} (${chunk.length} bytes)');
        _currentChunkIndex++;

        if (_currentChunkIndex >= _audioChunks.length) {
          // All chunks have been fed
          _isPlaying = false;
          _setState(AudioPlaybackState.stopped);
          _onPlaybackCompleted();
        }
      }
    } catch (e) {
      _handleError('Failed to feed audio data: $e');
    }
  }

  /// Start playing TTS audio chunks
  Future<void> startTTSPlayback(String requestId) async {
    try {
      debugPrint('AudioPlaybackService: Starting TTS playback for request: $requestId');
      
      // Stop any current playback
      await stopPlayback();
      
      // Reset state for new playback
      _currentRequestId = requestId;
      _audioChunks.clear();
      _currentChunkIndex = 0;
      _isPlaying = false;

      _setState(AudioPlaybackState.loading);
      
    } catch (e) {
      _handleError('Failed to start TTS playback: $e');
    }
  }

  /// Add audio chunk to the playback queue
  Future<void> addAudioChunk({
    required Uint8List audioData,
    required String chunkId,
    required bool isFinal,
    String? requestId,
    int? sampleRate,
    int? channels,
    int? bitDepth,
    String? format,
  }) async {
    try {
      // Verify this chunk belongs to the current request
      if (requestId != null && requestId != _currentRequestId) {
        debugPrint('AudioPlaybackService: Ignoring chunk for different request: $requestId');
        return;
      }

      debugPrint('AudioPlaybackService: Adding audio chunk $chunkId (${audioData.length} bytes, final: $isFinal)');

      // Check if we need to reinitialize with new audio format
      if (sampleRate != null && sampleRate != _currentSampleRate) {
        debugPrint('AudioPlaybackService: Sample rate mismatch detected! Current: ${_currentSampleRate}Hz, Received: ${sampleRate}Hz');
        debugPrint('AudioPlaybackService: This explains the slow playback - reinitializing with correct sample rate...');

        // Stop current playback
        await stopPlayback();

        // Reinitialize with correct sample rate
        await initialize(sampleRate: sampleRate, channelCount: channels ?? _currentChannels);
      }

      if (channels != null && channels != _currentChannels) {
        debugPrint('AudioPlaybackService: Channel count mismatch detected! Current: ${_currentChannels}, Received: $channels');

        // Stop current playback
        await stopPlayback();

        // Reinitialize with correct channel count
        await initialize(sampleRate: _currentSampleRate, channelCount: channels);
      }

      // Log format information for debugging
      if (sampleRate != null || channels != null || bitDepth != null || format != null) {
        debugPrint('AudioPlaybackService: Audio format - sampleRate: $sampleRate, channels: $channels, bitDepth: $bitDepth, format: $format');
      } else {
        // If no format metadata provided, try to detect from audio data
        _detectAudioFormat(audioData);
      }

      // Validate audio data
      if (audioData.isEmpty) {
        debugPrint('AudioPlaybackService: Warning - Empty audio chunk received');
        if (isFinal) {
          await _startPlayback();
        }
        return;
      }

      _audioChunks.add(audioData);

      if (isFinal) {
        debugPrint('AudioPlaybackService: Final chunk received, starting playback of ${_audioChunks.length} chunks');
        await _startPlayback();
      } else if (_audioChunks.length == 1) {
        // Start playing immediately when we get the first chunk
        debugPrint('AudioPlaybackService: First chunk received, starting playback');
        await _startPlayback();
      }

    } catch (e) {
      _handleError('Failed to add audio chunk: $e');
    }
  }

  /// Start playback using callback-based approach
  Future<void> _startPlayback() async {
    if (_audioChunks.isEmpty) {
      debugPrint('AudioPlaybackService: No audio chunks to play');
      return;
    }

    if (!_isInitialized) {
      debugPrint('AudioPlaybackService: PCM sound not initialized, initializing now...');
      await initialize();
    }

    try {
      debugPrint('AudioPlaybackService: Starting playback of ${_audioChunks.length} audio chunks');

      _setState(AudioPlaybackState.playing);
      _isPlaying = true;
      _currentChunkIndex = 0;

      // Start the playback by calling the callback manually (equivalent to FlutterPcmSound.start())
      _onFeedCallback(0);

    } catch (e) {
      _handleError('Failed to start playback: $e');
    }
  }



  /// Stop current playback
  Future<void> stopPlayback() async {
    try {
      // Clear audio chunks and reset state
      _audioChunks.clear();
      _currentRequestId = null;
      _isPlaying = false;
      _currentChunkIndex = 0;

      _setState(AudioPlaybackState.stopped);
      debugPrint('AudioPlaybackService: Playback stopped');

    } catch (e) {
      _handleError('Failed to stop playback: $e');
    }
  }

  /// Pause current playback (not supported by flutter_pcm_sound)
  Future<void> pausePlayback() async {
    try {
      _setState(AudioPlaybackState.paused);
      debugPrint('AudioPlaybackService: Playback paused (PCM streaming stopped)');
    } catch (e) {
      _handleError('Failed to pause playback: $e');
    }
  }

  /// Resume paused playback (not supported by flutter_pcm_sound)
  Future<void> resumePlayback() async {
    try {
      if (_audioChunks.isNotEmpty) {
        await _startPlayback();
        debugPrint('AudioPlaybackService: Playback resumed');
      }
    } catch (e) {
      _handleError('Failed to resume playback: $e');
    }
  }

  /// Handle playback completion
  void _onPlaybackCompleted() {
    debugPrint('AudioPlaybackService: Playback completed');
    _audioChunks.clear();
    _currentRequestId = null;
    _isPlaying = false;
    _currentChunkIndex = 0;
  }

  /// Set state and notify listeners
  void _setState(AudioPlaybackState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);
      debugPrint('AudioPlaybackService: State changed to $_state');
    }
  }

  /// Detect audio format from raw PCM data (heuristic approach)
  void _detectAudioFormat(Uint8List audioData) {
    if (audioData.length < 1000) return; // Need sufficient data for analysis

    // Analyze audio characteristics to guess sample rate
    // This is a heuristic approach when backend doesn't provide metadata

    // Calculate average amplitude to detect if audio seems too slow/fast
    int totalAmplitude = 0;
    int sampleCount = 0;

    for (int i = 0; i < audioData.length - 1; i += 2) {
      final sample = (audioData[i + 1] << 8) | audioData[i]; // Little-endian 16-bit
      totalAmplitude += sample.abs();
      sampleCount++;
    }

    final avgAmplitude = sampleCount > 0 ? totalAmplitude / sampleCount : 0;

    // If current sample rate is 16kHz but we're getting Eleven Labs audio,
    // it's likely 22.05kHz or 44.1kHz
    if (_currentSampleRate == 16000) {
      debugPrint('AudioPlaybackService: No format metadata provided. Current: 16kHz, but Eleven Labs commonly uses 22.05kHz or 44.1kHz');
      debugPrint('AudioPlaybackService: Average amplitude: $avgAmplitude, suggesting potential format mismatch');
      debugPrint('AudioPlaybackService: Consider testing with 22050Hz sample rate if audio sounds slow');
    }
  }

  /// Handle errors
  void _handleError(String error) {
    debugPrint('AudioPlaybackService Error: $error');
    _setState(AudioPlaybackState.error);
    _errorController.add(error);
  }

  /// Dispose resources
  void dispose() {
    debugPrint('AudioPlaybackService: Disposing...');
    _audioChunks.clear();
    _stateController.close();
    _errorController.close();
  }
}

/// Provider for AudioPlaybackService
final audioPlaybackServiceProvider = Provider<AudioPlaybackService>((ref) {
  final service = AudioPlaybackService();
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider for audio playback state
final audioPlaybackStateProvider = StreamProvider<AudioPlaybackState>((ref) {
  final service = ref.watch(audioPlaybackServiceProvider);
  return service.stateStream;
});
